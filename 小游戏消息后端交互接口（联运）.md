## 小游戏消息后端交互接口（联运）

|版本号   |更新日期   |更新内容   |
| ------------ | ------------ | ------------ |
| 1.0.0 | 20250919 | 第一版 |

### 1、接入说明

本文档旨在制定渠道服务端与研发之间的接口规范，明确传输参数及响应结果等内容。

### 2、签名校验

所有接口都需要对请求字段`sign` 做签名校验。

签名方式：将所有请求参数按字典顺升序排序（除了`sign`），然后用`&`拼接请求参数， 然后把签名密钥`sign_key`（渠道提供）拼接在字符串末尾，得到的结果进行`md5`处理生成待验证签名串，与请求参数中`sign`字段对比是否一致

伪代码如下：

```javascript
sign = md5(keyA=valueA&keyB=valueB&keyC=valueC + sign_key)
```

`golang`签名示例：

```go
func getSign(params map[string]interface{}, secret string) string {
	keys := []string{}
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	str := ""
	for _, k := range keys {
		str = str + k + "=" + fmt.Sprintf("%v", params[k]) + "&"
	}
	str = str[0:len(str)-1] + secret
	// 待签名字符串：app_id=abc&role_id=efg&timestamp=1621828880sign_key
	hasher := md5.New()
	hasher.Write([]byte(str))
	return hex.EncodeToString(hasher.Sum(nil))
}
func main() {
	params := map[string]interface{}{
		"app_id":    "abc",
		"role_id":   "efg",
		"timestamp": 1621828880,
	}
	sign := getSign(params, "sign_key")
	fmt.Println(sign) // 签名结果：777a2992e2ca685cd9fa5212c9ec87cb
}
```

### 3、接口列表

|序号   |接口   |提供方   |说明|
| ------------ | ------------ | ------------ | ------------ |
| 3.1 | 订阅事件回传   | 研发 |转发微信回传的用户订阅事件给研发|
| 3.2 | 推送事件 | 渠道 | 触发订阅事件之后，向玩家发送消息 |
| 3.3 | 邮件发送 | 研发 | 用于游戏内发邮件通知玩家和道具奖励发放 |
| 3.4 | 角色查询 | 研发 | 根据SDK账号返回角色列表 |

####  3.1 订阅事件回传

说明：转发微信回传的用户订阅事件给研发

接口地址：研发提供

请求方式：`POST`

请求格式：` application/json`

| 参数         | 类型      | 说明                                              | 参与签名 |
| ------------ | --------- | ------------------------------------------------- | -------- |
| `timestamp`  | `integer` | 时间戳（秒）                                      | 是       |
| `app_id`     | `string`  | 应用ID，CP提供，区分不同游戏                      | 是       |
| `sign`       | `string`  | 签名字段                                          | -        |
| `sm_user_id` | `string`  | 手盟核心帐号                                      | 是       |
| `state`      | `string`  | 订阅状态，用户接收是`accept` ，用户取消是`reject` | 是       |
| `tpl_id`     | `string`  | 模版ID，具体值微信后台查看                        | 是       |
| `openid`     | `string`  | 微信openid                                        | 是       |

请求示例：

```json
{
    "timestamp": 1621828880,
    "app_id": "app_id",
    "sign": "sign",
    "sm_user_id": "minigame_6499_xxxx",
    "state": "accept",
    "tpl_id": "XvQibaMtHk_xXyL9bNEQSRk6hl7_-0t_xnok1fLmvas",
    "openid": "xxx"
}
```

返回结果：

```json
{
    "code": 0,  # 非0，请在message中提示错误信息
    "message": "ok",
    "data": {}
}
```

####  3.2 推送事件

说明：触发订阅事件之后，向玩家发送消息

接口地址：`https://api.19meng.com/wegame/send_msg`

请求方式：`POST`

请求格式：` application/json`

| 参数         | 类型      | 说明                                                         | 参与签名 |
| ------------ | --------- | ------------------------------------------------------------ | -------- |
| `timestamp`  | `integer` | 时间戳（秒）                                                 | 是       |
| `app_id`     | `string`  | 应用ID，CP提供，区分不同游戏                                 | 是       |
| `sign`       | `string`  | 签名字段                                                     | -        |
| `openid_arr` | `array`   | 微信openid数组，最多1000个                                   | 否       |
| `tpl_id`     | `string`  | 模版ID                                                       | 是       |
| `page`       | `string`  | 点击模板卡片后的跳转页面，支持带参数,（示例index?foo=bar）   | 是       |
| `data`       | `object`  | 模板内容，统一使用 { "key1": { value: "val1" }, "key2": { value: "val2"} } 的格式  <a href="https://developers.weixin.qq.com/minigame/dev/api-backend/open-api/subscribe-message/subscribeMessage.send.html">微信文档</a>   <a href="https://developer.open-douyin.com/docs/resource/zh-CN/mini-app/develop/server/subscribe-notification/notify">抖音文档</a> | 否       |

请求示例：

```json
{
    "timestamp": 1621828880,
    "app_id": "app_id",
    "sign": "sign",
    "tpl_id": "XvQibaMtHk_xXyL9bNEQSRk6hl7_-0t_xnok1fLmvas",
    "openid_arr": ["x1","x2","x3"],
    "page": "index",
    "data": {"time1":{"value":"abc"},"thing2":{"value":"efg"}}
}
```

返回结果：

```json
{
    "code": 0,  # 非0，请在message中提示错误信息
    "message": "ok",
    "data": {}
}
```

####  3.3 邮件接口

说明：游戏内发邮件通知玩家，比如：游戏圈发送每日签到奖励

接口地址：CP提供

请求方式：`POST`

请求格式：` application/json`

| 参数        | 类型      | 说明                                | 参与签名 |
| ----------- | --------- | ----------------------------------- | -------- |
| `timestamp` | `integer` | 时间戳（秒）                        | 是       |
| `app_id`    | `string`  | 应用ID，CP提供，区分不同游戏        | 是       |
| `sign`      | `string`  | 签名字段                            | -        |
| `server_no` | `string`  | 区服编号                          | 是    |
| `role_id` | `string`  | CP角色ID | 是      |
| `product_id` | `string` | 奖励内容、序列化后的json字符串 | 是 |
| `text` | `string` | 邮件内容 | 是 |
| `title` | `string` | 邮件标题 | 是 |

请求示例：

```json
{
    "timestamp": 1621828880,
    "app_id": "app_id",
    "sign": "sign",
    "server_no": "9999",
    "role_id": "11211",
    "product_id": "[{\"id\":\"道具id1\",\"num\":1},{\"id\":\"道具id2\",\"num\":2}]",
    "text": "测试文本",
    "title": "标题"
}
```

返回结果：

```json
{
    "code": 0,  # 非0，请在message中提示错误信息
    "message": "ok"
}
```

####  3.4  角色查询

说明：根据SDK账号返回角色列表

接口地址：CP提供

请求方式：`POST`

请求格式：` application/json`

| 参数        | 类型      | 说明                                | 参与签名 |
| ----------- | --------- | ----------------------------------- | -------- |
| `timestamp` | `integer` | 时间戳（秒）                        | 是      |
| `sign`      | `string`  | 签名字段                            | -      |
| `user_id` | `string`  | SDK定义的用户 | 是       |

返回结果：

```json
{
	"code": 0, # 非0，请在message中提示错误信息
	"message": "ok",
	"data": [
        {
            "roleId": "10100123000002",
            "roleName": "示例玩家名称",
            "serverName": "S9999 示例区服",
            "serverId": "9999"
        }
	]
}
```